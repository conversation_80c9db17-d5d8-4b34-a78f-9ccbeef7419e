<div class="min-h-screen bg-white text-gray-800 flex flex-col">
  
  <!-- Header consistent sa design -->
  <header class="bg-gray-950 shadow-xl border-b-4 border-orange-500 relative z-10">
    <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4 flex justify-center items-center">
      <a routerLink="/" class="hover:opacity-80 transition duration-300 flex items-center">
        <img
          src="assets/images/BcLogo.png"
          alt="Benedicto College Logo"
          class="h-10 sm:h-14 md:h-16 lg:h-20 w-auto max-w-full object-contain"
          onerror="console.error('Logo failed to load:', this.src); this.style.border='2px solid red';"
          onload="console.log('Logo loaded successfully:', this.src);"
        >
      </a>
    </div>
  </header>

  <!-- Main Content -->
  <main class="flex-1 py-16 px-4 sm:px-6">
    <div class="contact-container">
      
      <!-- Page Title -->
      <div class="text-center mb-16">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Contact Us</h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Get in touch with Benedicto College Library Management System. We're here to help with your academic needs.
        </p>
      </div>

      <!-- Contact Content Grid -->
      <div class="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
        
        <!-- Contact Information -->
        <div class="contact-info-card">
          <h2 class="text-3xl font-bold mb-8 text-center">Contact Information</h2>
          
          <!-- Location -->
          <div class="contact-info-item">
            <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-lg">Location</h3>
              <p class="text-gray-300">Benedicto College Campus, Philippines</p>
            </div>
          </div>

          <!-- Email -->
          <div class="contact-info-item">
            <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-lg">Email</h3>
              <p class="text-gray-300">info&#64;benedictocollege.edu.ph</p>
            </div>
          </div>

          <!-- Phone -->
          <div class="contact-info-item">
            <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-lg">Phone</h3>
              <p class="text-gray-300">+63 (XXX) XXX-XXXX</p>
            </div>
          </div>

          <!-- Social Media -->
          <div class="mt-8 pt-8 border-t border-gray-600">
            <h3 class="text-xl font-bold mb-6 text-center">Connect With Us</h3>
            <div class="flex justify-center space-x-6">
              <a href="https://facebook.com/benedictocollege" target="_blank" class="text-blue-400 hover:text-blue-300 transition duration-300">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="https://instagram.com/benedictocollege" target="_blank" class="text-pink-400 hover:text-pink-300 transition duration-300">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z"/>
                </svg>
              </a>
              <a href="https://youtube.com/benedictocollege" target="_blank" class="text-red-400 hover:text-red-300 transition duration-300">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </a>
              <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-300 hover:text-white transition duration-300">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                </svg>
              </a>
            </div>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="contact-form">
          <h2 class="text-3xl font-bold mb-8 text-center text-gray-900">Send us a Message</h2>
          
          <form (ngSubmit)="onSubmit()" #contactFormRef="ngForm">
            <!-- First Name and Last Name Row -->
            <div class="grid md:grid-cols-2 gap-6 mb-6">
              <div class="form-group">
                <label for="firstName" class="block text-sm font-semibold text-gray-700 mb-2">First Name</label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  [(ngModel)]="contactForm.firstName"
                  class="form-input"
                  [class.error]="errors.firstName"
                  placeholder="Enter your first name"
                  required
                >
                <div *ngIf="errors.firstName" class="error-message">{{ errors.firstName }}</div>
              </div>
              
              <div class="form-group">
                <label for="lastName" class="block text-sm font-semibold text-gray-700 mb-2">Last Name</label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  [(ngModel)]="contactForm.lastName"
                  class="form-input"
                  [class.error]="errors.lastName"
                  placeholder="Enter your last name"
                  required
                >
                <div *ngIf="errors.lastName" class="error-message">{{ errors.lastName }}</div>
              </div>
            </div>

            <!-- Email -->
            <div class="form-group">
              <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                [(ngModel)]="contactForm.email"
                class="form-input"
                [class.error]="errors.email"
                placeholder="Enter your email address"
                required
              >
              <div *ngIf="errors.email" class="error-message">{{ errors.email }}</div>
            </div>

            <!-- Message -->
            <div class="form-group">
              <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Message</label>
              <textarea
                id="message"
                name="message"
                [(ngModel)]="contactForm.message"
                class="form-input"
                [class.error]="errors.message"
                placeholder="Enter your message here..."
                rows="6"
                required
              ></textarea>
              <div *ngIf="errors.message" class="error-message">{{ errors.message }}</div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="submit-btn">
              Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-black py-12 border-t-8 border-orange-500">
    <div class="container mx-auto px-6">
      <!-- Mobile/Tablet: Grid Layout (below 1000px) -->
      <div class="block xl:hidden">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Get in Touch Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-sm">Benedicto College Campus, Philippines</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm">info&#64;benedictocollege.edu.ph</span>
              </div>
              <div class="flex items-center justify-center md:justify-start">
                <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <span class="text-sm">+63 (XXX) XXX-XXXX</span>
              </div>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a routerLink="/" class="block text-gray-400 hover:text-orange-400 transition duration-300">Home</a>
              <a routerLink="/privacy-policy" class="block text-gray-400 hover:text-orange-400 transition duration-300">Privacy Policy</a>
              <a routerLink="/terms-of-service" class="block text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
              <a routerLink="/support" class="block text-gray-400 hover:text-orange-400 transition duration-300">Support</a>
              <a routerLink="/login" class="block text-gray-400 hover:text-orange-400 transition duration-300">Student Login</a>
            </div>
          </div>

          <!-- Connect with Us Section -->
          <div class="text-center md:text-left">
            <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
            <a href="https://facebook.com/benedictocollege" target="_blank" class="text-gray-400 hover:text-orange-400 transition duration-300 mr-6">Facebook</a>
            <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-400 hover:text-orange-400 transition duration-300 mr-6">Website</a>
            <a href="tel:+63321234567" class="text-gray-400 hover:text-orange-400 transition duration-300">Phone</a>
          </div>
        </div>
      </div>

      <!-- Desktop: Flexbox Layout (1000px+) -->
      <div class="hidden xl:flex justify-between items-start mb-8">
        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Contact Us</h3>
          <div class="space-y-3 text-gray-300">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span class="text-sm">Benedicto College Campus, Philippines</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              <span class="text-sm">info&#64;benedictocollege.edu.ph</span>
            </div>
            <div class="flex items-center">
              <svg class="w-5 h-5 text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
              <span class="text-sm">+63 (XXX) XXX-XXXX</span>
            </div>
          </div>
        </div>

        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
          <div class="space-y-2">
            <a routerLink="/" class="block text-gray-400 hover:text-orange-400 transition duration-300">Home</a>
            <a routerLink="/privacy-policy" class="block text-gray-400 hover:text-orange-400 transition duration-300">Privacy Policy</a>
            <a routerLink="/terms-of-service" class="block text-gray-400 hover:text-orange-400 transition duration-300">Terms of Service</a>
            <a routerLink="/support" class="block text-gray-400 hover:text-orange-400 transition duration-300">Support</a>
            <a routerLink="/login" class="block text-gray-400 hover:text-orange-400 transition duration-300">Student Login</a>
          </div>
        </div>

        <div class="flex-1">
          <h3 class="text-xl font-bold text-white mb-4">Connect with Us</h3>
          <a href="https://facebook.com/benedictocollege" target="_blank" class="text-gray-400 hover:text-orange-400 transition duration-300 mr-6">Facebook</a>
          <a href="https://benedictocollege.edu.ph" target="_blank" class="text-gray-400 hover:text-orange-400 transition duration-300 mr-6">Website</a>
          <a href="tel:+63321234567" class="text-gray-400 hover:text-orange-400 transition duration-300">Phone</a>
        </div>
      </div>

      <!-- Copyright Section -->
      <div class="border-t border-gray-700 pt-6">
        <div class="flex flex-col xl:flex-row justify-between items-center">
          <div class="text-gray-400 mb-4 xl:mb-0 text-center xl:text-left">
            &copy; 2025 Benedicto College Library Management System. All Rights Reserved.
          </div>
          <div class="text-gray-400 text-sm text-center xl:text-right">
            Your Education… Our Mission
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>

<script>
  // Mobile menu toggle functionality
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
      });
    }
  });
</script>
